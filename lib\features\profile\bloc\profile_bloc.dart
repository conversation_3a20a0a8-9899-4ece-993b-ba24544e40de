export 'profile_event.dart';
export 'profile_state.dart';

import 'dart:convert';

import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';

import 'profile_event.dart';
import 'profile_state.dart';

class ProfileBloc extends Bloc<ProfileEvent, ProfileState> {
  ProfileBloc() : super(const ProfileState.initial()) {
    on<ProfileEvent>((event, emit) async {
      await event.map(
        loadUserData: (e) => _mapLoadUserDataToState(emit),
        logout: (e) => _mapLogoutToState(emit),
      );
    });
  }

  Future<void> _mapLoadUserDataToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      String? userJson = AppPreferences.getUserdata();

      if (userJson != null && userJson.isNotEmpty) {
        Map<String, dynamic> userData = jsonDecode(userJson);
        emit(
          ProfileState.loaded(
            userName:
                userData['displayName'] ?? userData['phoneNumber'] ?? 'No Name',
            userEmail: userData['email'] ?? 'No Email',
            userGender: userData['gender'] ?? 'Not specified',
          ),
        );
      } else {
        emit(const ProfileState.loaded(
          userName: 'Guest User',
          userEmail: 'N/A',
          userGender: 'Not specified',
        ));
      }
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to load user data: $e'));
    }
  }

  Future<void> _mapLogoutToState(Emitter<ProfileState> emit) async {
    emit(const ProfileState.loading());
    try {
      getIt<AppBloc>().add(AppEvent.logoutRequested());
    } catch (e) {
      emit(ProfileState.error(message: 'Failed to logout: $e'));
    }
  }
}
