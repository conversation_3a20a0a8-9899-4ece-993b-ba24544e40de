import 'package:dio/dio.dart';
import 'package:rozana/core/network/api_client.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';

class OrderRepositoryImpl implements OrderRepositoryInterface {
  @override
  Future<Response?> createOrder({
    required String customerId,
    required String customerName,
    required String facilityId,
    required String facilityName,
    required num totalAmount,
    required List<Map<String, dynamic>> items,
  }) async {
    final orderPayload = {
      'customer_id': customerId,
      'customer_name': customerName,
      'facility_id': facilityId,
      'facility_name': facilityName,
      'status': 'pending',
      'total_amount': totalAmount,
      'items': items
    };
    
    return await ApiClient.sendHttpRequest(
      endUrl: 'create_order',
      method: HttpMethod.post,
      data: orderPayload,
    );
  }
}
