import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/themes/color_schemes.dart';
import 'package:rozana/widgets/custom_button.dart';

import '../../bloc/profile_bloc.dart';

class ProfileScreen extends StatelessWidget {
  const ProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Builder(
      builder: (context) {
        return Scaffold(
          appBar: AppBar(
            title: const Text('My Profile'),
            centerTitle: true,
            elevation: 0,
          ),
          body: BlocBuilder<ProfileBloc, ProfileState>(
            builder: (context, state) {
              return state.map(
                initial: (_) => const Center(
                    child: CircularProgressIndicator()), // Should be brief
                loading: (_) =>
                    const Center(child: CircularProgressIndicator()),
                loaded: (loadedState) => _buildProfileContent(
                  context,
                  loadedState.userName,
                  loadedState.userEmail,
                  loadedState.userGender,
                  false, // Not loading for UI interaction
                ),
                error: (errorState) => _buildProfileContent(
                  // Show content even on error, or an error widget
                  context,
                  'Error',
                  'Error',
                  'Error',
                  false,
                  errorMessage: errorState.message,
                ),
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildProfileContent(
    BuildContext context,
    String userName,
    String userEmail,
    String userGender,
    bool isLoading, {
    String? errorMessage,
  }) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          if (errorMessage != null)
            Padding(
              padding: const EdgeInsets.only(bottom: 16.0),
              child: Text(
                'Error: $errorMessage',
                style: const TextStyle(
                    color: Colors.red, fontWeight: FontWeight.bold),
                textAlign: TextAlign.center,
              ),
            ),
          const SizedBox(height: 20),
          _buildProfileAvatar(userName),
          const SizedBox(height: 24),
          _buildProfileInfo(userEmail, userGender),
          const SizedBox(height: 40),
          _buildLogoutButton(context, isLoading),
        ],
      ),
    );
  }

  Widget _buildProfileAvatar(String userName) {
    return Column(
      children: [
        CircleAvatar(
          radius: 50,
          backgroundColor: AppColors.primary.withValues(alpha: 0.2),
          child: Text(
            userName.isNotEmpty ? userName[0].toUpperCase() : '?',
            style: const TextStyle(
              fontSize: 40,
              fontWeight: FontWeight.bold,
              color: AppColors.primary,
            ),
          ),
        ),
        const SizedBox(height: 16),
        Text(
          userName,
          style: const TextStyle(
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildProfileInfo(String userEmail, String userGender) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            _buildInfoRow(Icons.email, 'Email', userEmail),
            const Divider(),
            _buildInfoRow(Icons.person, 'Gender', userGender),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(IconData icon, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8.0),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primary),
          const SizedBox(width: 16),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.grey,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context, bool isLoading) {
    return AppButton(
      text: 'Logout',
      onPressed: () {
        context.read<ProfileBloc>().add(const ProfileEvent.logout());
      },
      isLoading: isLoading,
      backgroundColor: Colors.red.shade700,
      width: double.infinity,
    );
  }
}
