name: rozana
description: "Rozana B2C App"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.6+7

environment:
  sdk: ^3.5.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  # http: ^1.2.0
  intl: ^0.20.2
  share_plus: ^10.0.0
  dio: ^5.3.2
  connectivity_plus: ^6.1.3
  lottie: ^3.1.2
  freezed_annotation: ^2.4.1
  
  # Dependency injection
  get_it: ^8.0.3
  
  # Local storage
  shared_preferences: ^2.2.2
  
  # State management
  flutter_bloc: ^9.1.1
  equatable: ^2.0.5   # For comparing BLoC states/events

  # Search
  algolia_helper_flutter: ^1.2.0
  typesense: ^0.5.2
  
  # Navigation
  go_router: ^15.1.3
  
  # UI components
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.0
  shimmer: ^3.0.0
  carousel_slider: ^5.0.0
  dartz: ^0.10.1
  
  # Firebase - Updated to latest compatible versions
  firebase_core: ^3.6.0
  firebase_auth: ^5.3.1
  google_sign_in: ^6.2.1
  
  # Location and Maps
  geolocator: ^11.1.0
  geocoding: ^2.2.2
  google_maps_flutter: ^2.12.2
  permission_handler: ^12.0.0+1
  google_maps_flutter_web: ^0.5.6

  # Phone number and OTP
  sms_autofill: ^2.3.0

  # Deep Linking and Analytics
  flutter_branch_sdk: ^8.5.0
  amplitude_flutter: ^3.16.5
  bloc: ^9.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^6.0.0
  build_runner: ^2.4.7
  freezed: ^2.4.5
  flutter_launcher_icons: ^0.14.4

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# Flutter Launcher Icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: false
  windows:
    generate: false
  macos:
    generate: false

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/
    - assets/categories/
    - assets/products/
    - assets/data/data.json
    - assets/icons/
    - assets/lotties/
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Manrope
      fonts:
        - asset: assets/fonts/manrope/Manrope-Bold.ttf
        - asset: assets/fonts/manrope/Manrope-ExtraBold.ttf
        - asset: assets/fonts/manrope/Manrope-ExtraLight.ttf
        - asset: assets/fonts/manrope/Manrope-Light.ttf
        - asset: assets/fonts/manrope/Manrope-Medium.ttf
        - asset: assets/fonts/manrope/Manrope-Regular.ttf
        - asset: assets/fonts/manrope/Manrope-SemiBold.ttf
        - asset: assets/fonts/manrope/Manrope-VariableFont_wght.ttf
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package