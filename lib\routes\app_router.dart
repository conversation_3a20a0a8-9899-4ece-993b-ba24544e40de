import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/data/models/category_model.dart';
import 'package:rozana/features/categories/bloc/categories_bloc.dart';
import 'package:rozana/features/home/<USER>/home%20bloc/home_bloc.dart';
import 'package:rozana/features/home/<USER>/screens/dashboard_screen.dart';
import 'package:rozana/features/location/presentation/screens/address_form_screen.dart';
import 'package:rozana/features/location/presentation/screens/map_screen.dart';
import 'package:rozana/features/products/bloc/product_listing_event.dart';
import 'package:rozana/features/products/presentation/screens/product_listing_screen.dart';

import '../core/dependency_injection/di_container.dart';
import '../data/models/adress_model.dart';
import '../features/auth/bloc/login_bloc/login_bloc.dart';
import '../features/cart/presentation/screens/cart_screen.dart';
import '../features/categories/presentation/screens/categories_screen.dart';
import '../features/location/presentation/screens/address_list_screen.dart';
import '../features/location/presentation/screens/location_detection_screen.dart';
import '../features/products/bloc/product_listing_bloc.dart';
import '../features/products/presentation/screens/product_detail_screen.dart';
import '../features/profile/bloc/profile_bloc.dart';
import '../features/profile/presentation/screen/profile_screen.dart';
import '../features/search/bloc/bloc/search_bloc.dart';
import '../features/search/presentation/screens/search_screen.dart';
import '../features/splash/presentation/screens/splash_screen.dart';
import '../features/auth/presentation/views/login_screen.dart';
import '../features/home/<USER>/screens/home_screen.dart';
import '../features/cart/presentation/screens/order_success_screen.dart';
import '../app/bloc/app_bloc.dart';

class RouteNames {
  // Auth routes
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgot-password';
  static const String verifyOtp = '/verify-otp';
  static const String resetPassword = '/reset-password';

  // Main routes
  static const String home = '/home';
  static const String categories = '/categories';
  static const String products = '/products';
  static const String productDetail = '/product-detail';
  static const String search = '/search';
  static const String cart = '/cart';
  static const String checkout = '/checkout';
  static const String orderSuccess = '/order-success';
  static const String orders = '/orders';
  static const String orderDetail = '/order-detail';

  // User profile routes
  static const String profile = '/profile';
  static const String editProfile = '/edit-profile';
  static const String addresses = '/addresses';
  static const String addAddress = '/add-address';
  static const String editAddress = '/edit-address';
  static const String detectLocation = '/address/detect';
  static const String wishlist = '/wishlist';
  static const String mapScreen = '/map-screen';

  // Settings and support routes
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  static const String support = '/support';
  static const String faq = '/faq';
  static const String about = '/about';
  static const String privacyPolicy = '/privacy-policy';
  static const String termsConditions = '/terms-conditions';
}

class AppRouter {
  static GoRouter createRouter(BuildContext context) {
    return GoRouter(
      navigatorKey: navigatorKey,
      initialLocation: RouteNames.splash, // Start at splash screen
      // Use BlocListener for redirection based on authentication state
      redirect: (BuildContext context, GoRouterState state) {
        final AppState splashState = context.read<AppBloc>().state;

        if (state.uri.toString().contains('shared-link')) {
          return RouteNames.home; // Prevent navigation for shared links
        }

        final redirectLocation = splashState.maybeMap(
          loaded: (value) {
            if (value.isAuthenticated) {
              // If the user is authenticated and trying to go to login/splash, redirect to home
              if (state.uri.toString() == RouteNames.login ||
                  state.uri.toString() == RouteNames.splash) {
                return RouteNames.home;
              }
            } else {
              // If the user is not authenticated and trying to go to home, redirect to login
              if (state.uri.toString() == RouteNames.profile) {
                return RouteNames.login;
              }
            }
            return null;
          },
          orElse: () => null,
        );

        return redirectLocation;
      },
      routes: [
        GoRoute(
          path: RouteNames.splash,
          builder: (context, state) => const SplashScreen(),
        ),
        GoRoute(
          path: RouteNames.login,
          builder: (context, state) => BlocProvider(
            create: (context) => getIt<LoginBloc>()
              ..add(const LoginEvent.initLogin()), // Create LoginBloc here
            child: const LoginScreen(),
          ),
        ),
        ShellRoute(
          builder: (context, state, child) {
            return BlocProvider(
              create: (context) => getIt<HomeBloc>()..add(HomeEvent.init()),
              child: HomeScreen(child: child),
            ); // Scaffold with BottomNav
          },
          routes: [
            GoRoute(
              path: RouteNames.home,
              pageBuilder: (context, state) =>
                  NoTransitionPage(child: const DashboardScreen()),
            ),
            GoRoute(
              path: RouteNames.categories,
              pageBuilder: (context, state) => NoTransitionPage(
                  child: BlocProvider(
                create: (context) => getIt<CategoriesBloc>()
                  ..add(CategoriesEvent.fetchCategories()),
                child: CategoriesScreen(),
              )),
            ),
            GoRoute(
              path: RouteNames.cart,
              pageBuilder: (context, state) =>
                  NoTransitionPage(child: CartScreen()),
            ),
            GoRoute(
              path: RouteNames.wishlist,
              pageBuilder: (context, state) =>
                  NoTransitionPage(child: SizedBox()),
            ),
            GoRoute(
              path: RouteNames.profile,
              pageBuilder: (context, state) => NoTransitionPage(
                  child: BlocProvider(
                create: (context) => getIt<ProfileBloc>()
                  ..add(const ProfileEvent.loadUserData()),
                child: ProfileScreen(),
              )),
            ),
          ],
        ),
        GoRoute(
            path: RouteNames.products,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra as Map<String, dynamic>;
              return BlocProvider(
                create: (context) => getIt<ProductListingBloc>()
                  ..add(ProductListingEvent.initial(
                    category: CategoryModel(
                        id: extras['categoryId'], name: extras['categoryName']),
                    subCategory: CategoryModel(
                      id: extras['subCategoryId'],
                      name: extras['subCategoryName'],
                    ),
                  )),
                child: const ProductListingScreen(),
              );
            }),
        GoRoute(
            path: RouteNames.productDetail,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra as Map<String, dynamic>;
              return ProductDetailPage(
                productData: extras['product'],
              );
            }),
        GoRoute(
          path: RouteNames.search,
          builder: (context, state) {
            String? query = state.uri.queryParameters['q'];

            return BlocProvider(
              create: (context) =>
                  getIt<SearchBloc>()..add(SearchEvent.init(query ?? '')),
              child: SearchScreen(),
            );
          },
        ),
        GoRoute(
          path: RouteNames.orderSuccess,
          builder: (context, state) {
            final Map<String, dynamic> args =
                state.extra as Map<String, dynamic>;
            return OrderSuccessScreen(
              orderId: args['orderId'],
              orderData: args['orderData'],
            );
          },
        ),
        GoRoute(
            path: RouteNames.addresses,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra != null
                  ? state.extra as Map<String, dynamic>
                  : {};
              final selectMode = extras['selectMode'] as bool? ?? false;
              final onAddressSelected =
                  extras['onAddressSelected'] as Function(AddressModel)?;
              return AddressListScreen(
                selectMode: selectMode,
                onAddressSelected: onAddressSelected,
              );
            }),
        GoRoute(
            path: RouteNames.addAddress,
            builder: (context, state) {
              return AddressFormScreen();
            }),
        GoRoute(
            path: RouteNames.editAddress,
            builder: (context, state) {
              if (state.extra is AddressModel) {
                // Editing existing address
                return AddressFormScreen(address: state.extra as AddressModel);
              } else if (state.extra is Map<String, dynamic>) {
                final extras = state.extra as Map<String, dynamic>;

                // Check if this is coordinate data from map screen
                if (extras.containsKey('latitude') &&
                    extras.containsKey('longitude')) {
                  return AddressFormScreen(
                    initialLatitude: extras['latitude'] as double?,
                    initialLongitude: extras['longitude'] as double?,
                  );
                } else {
                  // Try to create AddressModel from the map
                  try {
                    final address = AddressModel.fromJson(extras);
                    return AddressFormScreen(address: address);
                  } catch (e) {
                    // If parsing fails, create new address form
                    return const AddressFormScreen();
                  }
                }
              } else {
                // No data provided, create new address
                return const AddressFormScreen();
              }
            }),
        GoRoute(
            path: RouteNames.detectLocation,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra != null
                  ? state.extra as Map<String, dynamic>
                  : {};
              final onAddressSelected =
                  extras['onAddressSelected'] as Function(AddressModel?)?;
              final showSkip = extras['showSkip'] as bool? ?? true;
              return LocationDetectionScreen(
                onAddressSelected: onAddressSelected ?? (_) {},
                showSkip: showSkip,
              );
            }),
        GoRoute(
            path: RouteNames.mapScreen,
            builder: (context, state) {
              Map<String, dynamic> extras = state.extra != null
                  ? state.extra as Map<String, dynamic>
                  : {};
              final initialLatitude = extras['initialLatitude'] as double?;
              final initialLongitude = extras['initialLongitude'] as double?;
              final onLocationSelected =
                  extras['onLocationSelected'] as Function(double, double)?;
              return MapScreen(
                initialLatitude: initialLatitude,
                initialLongitude: initialLongitude,
                onLocationSelected: onLocationSelected,
              );
            })
      ],
    );
  }
}
