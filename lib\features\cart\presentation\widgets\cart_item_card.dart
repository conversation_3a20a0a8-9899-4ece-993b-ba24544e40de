import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import '../../../../core/themes/color_schemes.dart';

class CartItemCard extends StatefulWidget {
  final CartItemModel item;
  final Function(int)? onUpdateQuantity;
  final VoidCallback? onRemove;
  final bool isEditable;

  const CartItemCard({
    super.key,
    required this.item,
    this.onUpdateQuantity,
    this.onRemove,
    this.isEditable = true,
  });

  @override
  State<CartItemCard> createState() => _CartItemCardState();
}

class _CartItemCardState extends State<CartItemCard> {
  late int _quantity;

  @override
  void initState() {
    super.initState();
    _quantity = (widget.item.quantity ?? 0).toInt();
  }

  @override
  void didUpdateWidget(CartItemCard oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.item.quantity != widget.item.quantity) {
      _quantity = (widget.item.quantity ?? 0).toInt();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey[200]!),
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Product image
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SizedBox(
                width: 80,
                height: 80,
                child: _buildImage(),
              ),
            ),
            const SizedBox(width: 12),

            // Product details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Product name
                  Text(
                    widget.item.name ?? '--',
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                      color: Colors.black87,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 4),

                  // Unit
                  Text(
                    widget.item.unit ?? '0',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Price and quantity controls
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // Price
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          if ((widget.item.discountedPrice) != null &&
                              (widget.item.discountedPrice! > 0)) ...[
                            Text(
                              '₹${(widget.item.discountedPrice)!.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                            const SizedBox(height: 2),
                            Text(
                              '₹${widget.item.price?.toStringAsFixed(2)}',
                              style: TextStyle(
                                fontSize: 12,
                                decoration: TextDecoration.lineThrough,
                                color: Colors.grey[600],
                              ),
                            ),
                          ] else ...[
                            Text(
                              '₹${widget.item.price?.toStringAsFixed(2)}',
                              style: const TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ],
                      ),

                      // Quantity controls
                      _buildQuantityControls(),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImage() {
    // Check if the image is a network image or a local asset
    if (widget.item.imageUrl?.startsWith('http') ?? false) {
      // Network image
      return CachedNetworkImage(
        imageUrl: widget.item.imageUrl ?? '',
        fit: BoxFit.cover,
        placeholder: (context, url) => Center(
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: AppColors.primary,
          ),
        ),
        errorWidget: (context, url, error) => Container(
          color: Colors.grey[200],
          child: const Icon(
            Icons.image_not_supported_outlined,
            color: Colors.grey,
          ),
        ),
      );
    } else {
      // Local asset image
      return Image.asset(
        widget.item.imageUrl ?? '',
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) => Container(
          color: Colors.grey[200],
          child: const Icon(
            Icons.image_not_supported_outlined,
            color: Colors.grey,
          ),
        ),
      );
    }
  }

  Widget _buildQuantityControls() {
    return Row(
      children: [
        _buildRemoveButton(),
        const SizedBox(width: 8),
        _buildQuantitySelector(),
      ],
    );
  }

  Widget _buildRemoveButton() {
    if (!widget.isEditable || widget.onRemove == null) {
      return const SizedBox();
    }

    return GestureDetector(
      onTap: () {
        HapticFeedback.mediumImpact();
        widget.onRemove!();
      },
      child: Container(
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: Colors.red[50],
          shape: BoxShape.circle,
        ),
        child: Icon(
          Icons.delete_outline,
          color: Colors.red[700],
          size: 16,
        ),
      ),
    );
  }

  Widget _buildQuantitySelector() {
    // If not editable, just show the quantity
    if (!widget.isEditable) {
      return Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: Colors.grey[100],
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          'Qty: $_quantity',
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      );
    }

    return Row(
      children: [
        _buildQuantityButton(
          icon: Icons.remove,
          onPressed: _quantity > 1 && widget.onUpdateQuantity != null
              ? () {
                  HapticFeedback.lightImpact();
                  // Update local state immediately for responsive UI
                  setState(() {
                    _quantity--;
                  });
                  // Then notify parent
                  widget.onUpdateQuantity!(_quantity);
                }
              : null,
        ),
        Container(
          width: 36,
          alignment: Alignment.center,
          child: Text(
            '$_quantity',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        _buildQuantityButton(
          icon: Icons.add,
          onPressed: widget.onUpdateQuantity != null
              ? () {
                  HapticFeedback.lightImpact();
                  setState(() {
                    _quantity++;
                  });
                  widget.onUpdateQuantity!(_quantity);
                }
              : null,
        ),
      ],
    );
  }

  Widget _buildQuantityButton(
      {required IconData icon, VoidCallback? onPressed}) {
    return InkWell(
      onTap: onPressed,
      child: Container(
        width: 28,
        height: 28,
        alignment: Alignment.center,
        child: Icon(
          icon,
          size: 16,
          color: onPressed != null ? AppColors.primary : Colors.grey[400],
        ),
      ),
    );
  }
}
