import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rozana/core/utils/text_field_manager.dart';
import 'package:rozana/widgets/custom_textfield.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../core/utils/app_validators.dart';
import '../../../../data/models/adress_model.dart';
import '../../../../routes/app_router.dart';
import '../../../../widgets/custom_button.dart';
import '../../services/adress_services.dart';

class AddressFormScreen extends StatefulWidget {
  final AddressModel? address;
  final double? initialLatitude;
  final double? initialLongitude;

  const AddressFormScreen({
    super.key,
    this.address,
    this.initialLatitude,
    this.initialLongitude,
  });

  @override
  State<AddressFormScreen> createState() => _AddressFormScreenState();
}

class _AddressFormScreenState extends State<AddressFormScreen> {
  final _formKey = GlobalKey<FormState>();
  final _addressService = AddressService();

  final _addressLine1Controller = TextFieldManager();
  final _landmarkController = TextFieldManager();
  final _cityController = TextFieldManager();
  final _stateController = TextFieldManager();
  final _pincodeController = TextFieldManager();
  final _searchController = TextFieldManager();

  bool _isLoading = false;
  bool _isLoadingLocation = false;
  bool _isDefault = false;
  String _addressType = 'home';

  double _latitude = 0.0;
  double _longitude = 0.0;

  GoogleMapController? _mapController;
  Set<Marker> _markers = {};

  @override
  void initState() {
    super.initState();

    if (widget.address != null) {
      _populateFormWithAddress(widget.address!);
    } else if (widget.initialLatitude != null &&
        widget.initialLongitude != null) {
      // Handle coordinates passed from map screen
      _initializeWithCoordinates(
          widget.initialLatitude!, widget.initialLongitude!);
    } else {
      _getCurrentLocation();
    }
  }

  @override
  void dispose() {
    _addressLine1Controller.dispose();
    _landmarkController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _searchController.dispose();
    _mapController?.dispose();
    super.dispose();
  }

  void _populateFormWithAddress(AddressModel address) {
    _addressLine1Controller.text = address.addressLine1 ?? '';
    _landmarkController.text = address.landmark ?? '';
    _cityController.text = address.city ?? '';
    _stateController.text = address.state ?? '';
    _pincodeController.text = address.pincode ?? '';
    _isDefault = address.isDefault ?? false;
    _addressType = address.addressType ?? '';
    _latitude = (address.latitude ?? 0).toDouble();
    _longitude = (address.longitude ?? 0).toDouble();

    // Update map marker
    _updateMapMarker(LatLng(_latitude, _longitude));
  }

  Future<void> _initializeWithCoordinates(
      double latitude, double longitude) async {
    setState(() {
      _isLoadingLocation = true;
      _latitude = latitude;
      _longitude = longitude;
    });

    try {
      // Get address details for the provided coordinates
      final placemarks = await _addressService.getAddressFromCoordinates(
        latitude,
        longitude,
      );

      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;

        setState(() {
          _addressLine1Controller.text = placemark.street ?? '';
          _cityController.text = placemark.locality ?? '';
          _stateController.text = placemark.administrativeArea ?? '';
          _pincodeController.text = placemark.postalCode ?? '';

          // Update map marker
          _updateMapMarker(LatLng(_latitude, _longitude));
        });
      }
    } catch (e) {
      _showSnackBar('Failed to get address from coordinates');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final hasPermission = await _addressService.checkLocationPermission();

      if (!hasPermission) {
        final permissionGranted =
            await _addressService.requestLocationPermission();

        if (!permissionGranted) {
          _showSnackBar('Location permission denied');
          setState(() {
            _isLoadingLocation = false;
          });
          return;
        }
      }

      final position = await _addressService.getCurrentPosition();

      if (position != null) {
        final placemarks = await _addressService.getAddressFromCoordinates(
          position.latitude,
          position.longitude,
        );

        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;

          setState(() {
            _latitude = position.latitude;
            _longitude = position.longitude;

            _addressLine1Controller.text = placemark.street ?? '';
            _cityController.text = placemark.locality ?? '';
            _stateController.text = placemark.administrativeArea ?? '';
            _pincodeController.text = placemark.postalCode ?? '';

            // Update map marker
            _updateMapMarker(LatLng(_latitude, _longitude));
          });
        }
      }
    } catch (e) {
      _showSnackBar('Failed to get current location');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _navigateToLocationSelection() async {
    final result = await context.push(
      RouteNames.mapScreen,
      extra: {
        'initialLatitude': _latitude,
        'initialLongitude': _longitude,
      },
    );

    if (result != null && result is Map<String, dynamic>) {
      final latitude = result['latitude'] as double?;
      final longitude = result['longitude'] as double?;

      if (latitude != null && longitude != null) {
        setState(() {
          _latitude = latitude;
          _longitude = longitude;
        });

        // Update map marker
        _updateMapMarker(LatLng(_latitude, _longitude));

        // Get address details for the selected location
        _onMarkerDragEnd(LatLng(_latitude, _longitude));
      }
    }
  }

  void _updateMapMarker(LatLng position) {
    setState(() {
      _markers = {
        Marker(
          markerId: const MarkerId('selected_location'),
          position: position,
          draggable: true,
          onDragEnd: _onMarkerDragEnd,
        ),
      };
    });

    _mapController?.animateCamera(
      CameraUpdate.newCameraPosition(
        CameraPosition(
          target: position,
          zoom: 16,
        ),
      ),
    );
  }

  void _onMarkerDragEnd(LatLng position) async {
    setState(() {
      _latitude = position.latitude;
      _longitude = position.longitude;
      _isLoadingLocation = true;
    });

    try {
      final placemarks = await _addressService.getAddressFromCoordinates(
        position.latitude,
        position.longitude,
      );

      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;

        setState(() {
          _addressLine1Controller.text = placemark.street ?? '';
          _cityController.text = placemark.locality ?? '';
          _stateController.text = placemark.administrativeArea ?? '';
          _pincodeController.text = placemark.postalCode ?? '';
        });
      }
    } catch (e) {
      _showSnackBar('Failed to get address from location');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _searchAddress(String query) async {
    if (query.isEmpty) {
      return;
    }

    setState(() {
      _isLoadingLocation = true;
    });

    try {
      final locations = await _addressService.searchAddresses(query);

      if (locations != null && locations.isNotEmpty) {
        final location = locations.first;
        final placemarks =
            await _addressService.getPlacemarkFromLocation(location);

        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;

          setState(() {
            _latitude = location.latitude;
            _longitude = location.longitude;

            _addressLine1Controller.text = placemark.street ?? '';
            _cityController.text = placemark.locality ?? '';
            _stateController.text = placemark.administrativeArea ?? '';
            _pincodeController.text = placemark.postalCode ?? '';

            // Update map marker
            _updateMapMarker(LatLng(_latitude, _longitude));
          });
        }
      } else {
        _showSnackBar('No results found for "$query"');
      }
    } catch (e) {
      _showSnackBar('Failed to search address');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _saveAddress() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final addressId = widget.address?.id ??
          DateTime.now().millisecondsSinceEpoch.toString();

      final address = AddressModel(
        id: addressId,
        fullAddress: _buildFullAddress(),
        addressLine1: _addressLine1Controller.text,
        landmark: _landmarkController.text.isNotEmpty
            ? _landmarkController.text
            : null,
        city: _cityController.text,
        state: _stateController.text,
        pincode: _pincodeController.text,
        latitude: _latitude,
        longitude: _longitude,
        addressType: _addressType,
        isDefault: _isDefault,
      );

      await _addressService.saveAddress(address);

      if (mounted) {
        // Navigate back to homepage instead of just popping
        context.go(RouteNames.home);
      }
    } catch (e) {
      _showSnackBar('Failed to save address');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _buildFullAddress() {
    final parts = [
      _addressLine1Controller.text,
      _landmarkController.text,
      _cityController.text,
      _stateController.text,
      _pincodeController.text,
    ];

    return parts.where((part) => part.isNotEmpty).join(', ');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            Text(widget.address != null ? 'Edit Address' : 'Add New Address'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: _isLoadingLocation
          ? const Center(child: CircularProgressIndicator())
          : _buildForm(),
    );
  }

  Widget _buildForm() {
    return Form(
      key: _formKey,
      child: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          // Select location on map button
          AppButton(
            text: 'Select Location on Map',
            onPressed: _navigateToLocationSelection,
            isOutlined: true,
            prefixIcon: const Icon(Icons.map, size: 18),
          ),
          const SizedBox(height: 24),

          // Address type selector
          Text(
            'Address Type',
            style: TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          _buildAddressTypeSelector(),
          const SizedBox(height: 24),

          // Address form fields
          ValueListenableBuilder(
              valueListenable: _addressLine1Controller.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Address Line',
                  field: CustomTextField(
                    hintText: 'House/Flat No., Building, Street',
                    controller: _addressLine1Controller.controller,
                    // focusNode: _addressLine1Controller.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.home),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          Titledfield(
            title: 'Landmark (Optional)',
            field: CustomTextField(
                hintText: 'Nearby landmark',
                controller: _landmarkController.controller,
                // focusNode: _landmarkController.focusNode,
                decoration: InputDecoration(
                  prefixIcon: const Icon(Icons.location_on),
                )),
          ),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _cityController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'City',
                  field: CustomTextField(
                    hintText: 'City',
                    controller: _cityController.controller,
                    // focusNode: _cityController.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.location_city),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _stateController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'State',
                  field: CustomTextField(
                    hintText: 'State',
                    controller: _stateController.controller,
                    // focusNode: _stateController.focusNode,
                    decoration: InputDecoration(
                      prefixIcon: const Icon(Icons.map),
                    ),
                  ),
                  errorText: error,
                );
              }),
          const SizedBox(height: 16),

          ValueListenableBuilder(
              valueListenable: _pincodeController.errorText,
              builder: (context, error, _) {
                return Titledfield(
                  title: 'Pincode',
                  field: CustomTextField(
                      hintText: 'Pincode',
                      controller: _pincodeController.controller,
                      // focusNode: _pincodeController.focusNode,
                      keyboardType: TextInputType.number,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                        LengthLimitingTextInputFormatter(6),
                      ],
                      decoration: InputDecoration(
                        prefixIcon: const Icon(Icons.pin_drop),
                      )),
                  errorText: error,
                );
              }),
          const SizedBox(height: 24),

          // Default address checkbox
          Row(
            children: [
              Checkbox(
                value: _isDefault,
                onChanged: (value) {
                  setState(() {
                    _isDefault = value ?? false;
                  });
                },
                activeColor: AppColors.primary,
              ),
              const Text('Set as default address'),
            ],
          ),
          const SizedBox(height: 24),

          // Save button
          AppButton(
            text: 'Save Address',
            onPressed: () {
              ValidationState addressValidationState =
                  AppValidator.emptyStringValidator(
                      _addressLine1Controller.text, 'Please enter address');

              ValidationState cityValidationState =
                  AppValidator.emptyStringValidator(
                      _cityController.text, 'Please enter city');
              ValidationState stateValidationState =
                  AppValidator.emptyStringValidator(
                      _stateController.text, 'Please enter state');
              ValidationState pincodeValidationState =
                  AppValidator.emptyStringValidator(
                      _pincodeController.text, 'Please enter pincode',
                      minLength: 6,
                      lengthMessage: 'Please enter a valid 6-digit pincode');

              if (!addressValidationState.valid) {
                _addressLine1Controller
                    .throwError(addressValidationState.message ?? '');
                return;
              }
              _addressLine1Controller.throwError('');
              if (!cityValidationState.valid) {
                _cityController.throwError(cityValidationState.message ?? '');
                return;
              }
              _cityController.throwError('');
              if (!stateValidationState.valid) {
                _stateController.throwError(stateValidationState.message ?? '');
                return;
              }
              _stateController.throwError('');
              if (!pincodeValidationState.valid) {
                _pincodeController
                    .throwError(pincodeValidationState.message ?? '');
                return;
              }
              _pincodeController.throwError('');
              _saveAddress();
            },
            isLoading: _isLoading,
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildAddressTypeSelector() {
    return Row(
      children: [
        _buildAddressTypeOption('home', 'Home', Icons.home),
        const SizedBox(width: 16),
        _buildAddressTypeOption('work', 'Work', Icons.work),
        const SizedBox(width: 16),
        _buildAddressTypeOption('other', 'Other', Icons.place),
      ],
    );
  }

  Widget _buildAddressTypeOption(String type, String label, IconData icon) {
    final isSelected = _addressType == type;

    return Expanded(
      child: InkWell(
        onTap: () {
          setState(() {
            _addressType = type;
          });
        },
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppColors.primary : Colors.grey.shade300,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(8),
            color: isSelected
                ? AppColors.primary.withValues(alpha: 0.1)
                : Colors.transparent,
          ),
          child: Column(
            children: [
              Icon(
                icon,
                color: isSelected ? AppColors.primary : Colors.grey,
              ),
              const SizedBox(height: 4),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? AppColors.primary : Colors.grey.shade700,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
