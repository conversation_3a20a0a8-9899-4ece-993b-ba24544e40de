import 'dart:convert';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/data/models/cart_model.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'cart_event.dart';
import 'cart_state.dart';

class CartBloc extends Bloc<CartEvent, CartState> {
  CartBloc() : super(CartState.initial()) {
    on<CartInit>(_onInit);
    on<CartAddItem>(_onAddItem);
    on<CartRemoveItem>(_onRemoveItem);
    on<CartUpdateQuantity>(_onUpdateQuantity);
    on<CartClear>(_onClear);
    on<CartApplyCoupon>(_onApplyCoupon);
    on<CartRemoveCoupon>(_onRemoveCoupon);
    on<CartImportData>(_onImportData);
  }

  Future<void> _onInit(CartInit event, Emitter<CartState> emit) async {
    emit(state.copyWith(isLoading: true));
    try {
      String? cartJson = AppPreferences.getCartData();
      if (cartJson != null) {
        Map<String, dynamic> data = jsonDecode(cartJson);
        emit(state.copyWith(cart: CartModel.fromJson(data)));
      }
    } catch (e) {
      emit(state.copyWith(error: 'Failed to load cart'));
    }
    emit(state.copyWith(isLoading: false));
  }

  Future<void> _onAddItem(CartAddItem event, Emitter<CartState> emit) async {
    CartModel cart = state.cart;
    List<CartItemModel> items = [...(cart.items ?? [])];
    final existingIndex =
        items.indexWhere((i) => i.productId == event.item.productId);

    if (existingIndex != -1) {
      CartItemModel updatedItem = items[existingIndex].copyWith(
        quantity:
            (items[existingIndex].quantity ?? 0) + (event.item.quantity ?? 0),
      );
      items[existingIndex] = updatedItem;
    } else {
      items.add(CartItemModel(
        id: DateTime.now().millisecondsSinceEpoch.toString(),
        productId: event.item.productId,
        name: event.item.name,
        price: event.item.price,
        imageUrl: event.item.imageUrl,
        quantity: event.item.quantity,
        unit: event.item.unit,
        discountedPrice: event.item.discountedPrice,
        customizations: event.item.customizations,
        facilityId: event.item.facilityId,
        facilityName: event.item.facilityName,
        skuID: event.item.skuID,
      ));
    }

    CartModel updatedCart = cart.copyWith(items: items).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart));
  }

  Future<void> _onRemoveItem(
      CartRemoveItem event, Emitter<CartState> emit) async {
    final updatedCart = state.cart
        .copyWith(
          items: state.cart.items?.where((i) => i.id != event.itemId).toList(),
        )
        .recalculate();

    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart));
  }

  Future<void> _onUpdateQuantity(
      CartUpdateQuantity event, Emitter<CartState> emit) async {
    final items = state.cart.items?.map((item) {
      if (item.id == event.itemId) {
        return item.copyWith(quantity: event.quantity);
      }
      return item;
    }).toList();

    final updatedCart = state.cart.copyWith(items: items).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart));
  }

  Future<void> _onClear(CartClear event, Emitter<CartState> emit) async {
    final clearedCart = CartModel();

    emit(state.copyWith(cart: clearedCart));
    await _saveToStorage(clearedCart);
  }

  Future<void> _onApplyCoupon(
      CartApplyCoupon event, Emitter<CartState> emit) async {
    CartModel cart = state.cart;
    num discount = 0;

    num subTotal = cart.subTotal ?? 0;

    switch (event.code.toUpperCase()) {
      case 'WELCOME10':
        discount = subTotal * 0.1;
        break;
      case 'FLAT50':
        discount = subTotal < 50 ? subTotal : 50;
        break;
      case 'FREESHIP':
        emit(state.copyWith(cart: cart.copyWith(deliveryFee: 0)));
        return;
      case 'SUMMER20':
        discount = subTotal * 0.2;
        break;
      default:
        emit(state.copyWith(error: 'Invalid coupon code'));
        return;
    }

    final updatedCart = cart.copyWith(discount: discount).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart, appliedCoupon: event.code));
  }

  Future<void> _onRemoveCoupon(
      CartRemoveCoupon event, Emitter<CartState> emit) async {
    num resetFee =
        state.appliedCoupon == 'FREESHIP' ? 40 : state.cart.deliveryFee ?? 0;
    final updatedCart =
        state.cart.copyWith(discount: 0, deliveryFee: resetFee).recalculate();
    await _saveToStorage(updatedCart);
    emit(state.copyWith(cart: updatedCart, appliedCoupon: null));
  }

  Future<void> _onImportData(
      CartImportData event, Emitter<CartState> emit) async {
    try {
      final data = jsonDecode(event.jsonData);
      final importedCart = CartModel.fromJson(data);
      await _saveToStorage(importedCart);
      emit(state.copyWith(cart: importedCart));
    } catch (_) {
      emit(state.copyWith(error: 'Invalid cart data'));
    }
  }

  Future<void> _saveToStorage(CartModel cart) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('cart_data', jsonEncode(cart.toJson()));
  }
}
