import 'dart:convert';
import 'package:flutter/services.dart';

class SampleDataService {
  Future<List<Map<String, dynamic>>> getCategories() async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);
    return List<Map<String, dynamic>>.from(data['categories']);
  }

  Future<List<Map<String, dynamic>>> getSubCategories(
      {String categoryID = ''}) async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);
    if (categoryID.isEmpty) {
      return List<Map<String, dynamic>>.from(data['subCategories'])
          .take(20)
          .toList();
    }
    return List<Map<String, dynamic>>.from(data['subCategories']
        .where((subCategory) => subCategory['categoryID'] == categoryID)
        .toList());
  }

  Future<List<Map<String, dynamic>>> getProducts(
      {String subCategoryID = ''}) async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);

    if (subCategoryID.isEmpty) {
      final allProducts = List<Map<String, dynamic>>.from(data['products']);
      return allProducts.take(50).toList();
    }

    return List<Map<String, dynamic>>.from(data['products']
        .where((product) => product['subCategoryID'] == subCategoryID)
        .toList());
  }

  Future<List<Map<String, dynamic>>> getFeaturedProducts() async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');

    final data = jsonDecode(jsonData);
    return List<Map<String, dynamic>>.from(data['featuredProducts']);
  }

  Future<List<Map<String, dynamic>>> getBanners() async {
    final String jsonData =
        await rootBundle.loadString('assets/data/data.json');
    final data = jsonDecode(jsonData);
    return List<Map<String, dynamic>>.from(data['banners']);
  }
}
