import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:rozana/app/bloc/app_bloc.dart';
import 'package:rozana/core/dependency_injection/di_container.dart';
import 'package:rozana/core/services/app_preferences_service.dart';
import 'package:rozana/data/models/cart_item_model.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/cart/bloc/cart_event.dart';
import 'package:rozana/features/cart/bloc/cart_state.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_button.dart';
import '../widgets/cart_item_card.dart';
import '../widgets/cart_summary_card.dart';
import '../widgets/empty_cart.dart';
import '../widgets/payment_method_selector.dart';

class CartScreen extends StatefulWidget {
  const CartScreen({super.key});

  @override
  State<CartScreen> createState() => _CartScreenState();
}

class _CartScreenState extends State<CartScreen> {
  final CartBloc _cartBloc = getIt<CartBloc>();
  final TextEditingController _couponController = TextEditingController();
  final _addressFormKey = GlobalKey<FormState>();
  bool _isApplyingCoupon = false;
  bool _showCouponField = false;
  bool _isProcessingOrder = false;

  // Checkout data with default address
  final Map<String, dynamic> _checkoutData = {
    'paymentMethod': 'cash', // ID for Cash on Delivery
    'address': {
      'fullName': 'John Doe',
      'phoneNumber': '9876543210',
      'addressLine1': '123 Main Street',
      'addressLine2': 'Apartment 4B',
      'city': 'Mumbai',
      'state': 'Maharashtra',
      'pincode': '400001',
      'addressType': 'Home',
    },
    'deliverySlot': 'standard',
  };

  @override
  void dispose() {
    _couponController.dispose();
    super.dispose();
  }

  void _showClearCartConfirmation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cart'),
        content: const Text(
            'Are you sure you want to remove all items from your cart?'),
        actions: [
          TextButton(
            onPressed: () => context.pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              _cartBloc.add(CartEvent.clear());
              context.pop();
            },
            child: const Text('Clear', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _applyCoupon() async {
    if (_couponController.text.isEmpty) return;

    setState(() {
      _isApplyingCoupon = true;
    });

    _cartBloc.add(CartEvent.applyCoupon(_couponController.text));

    setState(() {
      _isApplyingCoupon = false;
      // if (success) {
      //   _showCouponField = false;
      //   _couponController.clear();
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     const SnackBar(
      //       content: Text('Coupon applied successfully!'),
      //       backgroundColor: Colors.green,
      //     ),
      //   );
      // } else {
      //   ScaffoldMessenger.of(context).showSnackBar(
      //     const SnackBar(
      //       content: Text('Invalid coupon code'),
      //       backgroundColor: Colors.red,
      //     ),
      //   );
      // }
    });
  }

  @override
  Widget build(BuildContext context) {
    // Use ChangeNotifierProvider.value at the root level but with a more stable structure
    return Scaffold(
      backgroundColor: Colors.grey[50],
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0.5,
        title: const Text(
          'My Cart',
          style: TextStyle(
            color: Colors.black87,
            fontSize: 16,
            fontWeight: FontWeight.w600,
          ),
        ),
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black87),
          onPressed: () => context.pop(),
        ),
        actions: [
          BlocBuilder<CartBloc, CartState>(
            builder: (context, state) {
              if (state.cart.items?.isEmpty ?? false) return const SizedBox();

              return IconButton(
                icon: const Icon(Icons.delete_outline, color: Colors.red),
                onPressed: () {
                  HapticFeedback.mediumImpact();
                  _showClearCartConfirmation(context);
                },
              );
            },
          ),
        ],
      ),
      body: BlocBuilder<CartBloc, CartState>(
        buildWhen: (previous, current) =>
            previous.isLoading != current.isLoading,
        builder: (context, state) {
          if (state.isLoading) {
            return const Center(child: CircularProgressIndicator());
          }

          return BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) =>
                previous.cart.items != current.cart.items,
            builder: (context, state) {
              if (state.cart.items?.isEmpty ?? true) {
                return const EmptyCart();
              }

              return _buildCartContentOptimized(context);
            },
          );
        },
      ),
      bottomNavigationBar: BlocBuilder<CartBloc, CartState>(
        buildWhen: (previous, current) =>
            previous.cart.items != current.cart.items ||
            previous.cart.total != current.cart.total ||
            previous.cart.totalItems != current.cart.totalItems,
        builder: (context, state) {
          if (state.cart.items?.isEmpty ?? true) {
            return const SizedBox();
          }

          return _buildCheckoutBar(context);
        },
      ),
    );
  }

  Widget _buildCartContentOptimized(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Cart items without separate scrolling
          BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) =>
                previous.cart.items != current.cart.items,
            builder: (context, state) {
              return Column(
                children: state.cart.items
                        ?.map((item) => _buildCartItemWithSelector(item))
                        .toList() ??
                    [],
              );
            },
          ),

          // const SizedBox(height: 16),

          // Coupon section
          // _buildCouponSection(),

          const SizedBox(height: 16),

          // Cart summary with Selector to update when cart totals change
          BlocBuilder<CartBloc, CartState>(
            buildWhen: (previous, current) => previous.cart != current.cart,
            builder: (context, state) => CartSummaryCard(cart: state.cart),
          ),

          const SizedBox(height: 24),

          // Delivery Address Section
          const Text(
            'Delivery Address',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildAddressCard(),

          const SizedBox(height: 24),

          // Payment Method Section
          const Text(
            'Payment Method',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          PaymentMethodSelector(
            selectedMethod: _checkoutData['paymentMethod'],
            onMethodSelected: (method) {
              setState(() {
                _checkoutData['paymentMethod'] = method;
              });
            },
          ),

          const SizedBox(height: 100), // Space for bottom bar
        ],
      ),
    );
  }

  Widget _buildCartItemWithSelector(CartItemModel item) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: CartItemCard(
        key: ValueKey(item.id), // Add key for better Flutter diffing
        item: item,
        onUpdateQuantity: (quantity) async {
          HapticFeedback.lightImpact();
          // Use async/await to ensure operation completes
          _cartBloc.add(CartEvent.updateQuantity(item.id ?? '', quantity));
        },
        onRemove: () async {
          HapticFeedback.mediumImpact();
          // Use async/await to ensure operation completes
          _cartBloc.add(CartEvent.removeItem(item.id ?? ''));
        },
      ),
    );
  }

  Widget _buildCouponSection() {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(color: Colors.grey[200]!),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: BlocBuilder<CartBloc, CartState>(
          builder: (context, state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.local_offer_outlined,
                          size: 18,
                          color: Colors.grey[700],
                        ),
                        const SizedBox(width: 8),
                        Text(
                          'Apply Coupon',
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            color: Colors.grey[800],
                          ),
                        ),
                      ],
                    ),
                    if ((state.cart.discount ?? 0) > 0)
                      Row(
                        children: [
                          Text(
                            '₹${state.cart.discount?.toStringAsFixed(2)} off',
                            style: const TextStyle(
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                              color: Colors.green,
                            ),
                          ),
                          const SizedBox(width: 4),
                          GestureDetector(
                            onTap: () {
                              HapticFeedback.lightImpact();
                              _cartBloc.add(CartEvent.removeCoupon());
                            },
                            child: const Icon(
                              Icons.close,
                              size: 16,
                              color: Colors.red,
                            ),
                          ),
                        ],
                      ),
                  ],
                ),
                if (_showCouponField) ...[
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: TextField(
                          controller: _couponController,
                          decoration: InputDecoration(
                            hintText: 'Enter coupon code',
                            hintStyle: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[500],
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                              borderSide: BorderSide(color: Colors.grey[300]!),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          style: const TextStyle(fontSize: 14),
                        ),
                      ),
                      const SizedBox(width: 8),
                      SizedBox(
                        height: 40,
                        child: ElevatedButton(
                          onPressed: _isApplyingCoupon ? null : _applyCoupon,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                          ),
                          child: _isApplyingCoupon
                              ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: Colors.white,
                                  ),
                                )
                              : const Text(
                                  'Apply',
                                  style: TextStyle(
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                        ),
                      ),
                    ],
                  ),
                ] else if (state.cart.discount == 0) ...[
                  const SizedBox(height: 8),
                  GestureDetector(
                    onTap: () {
                      HapticFeedback.lightImpact();
                      setState(() {
                        _showCouponField = true;
                      });
                    },
                    child: Row(
                      children: [
                        Icon(
                          Icons.add_circle_outline,
                          size: 16,
                          color: AppColors.primary,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Add Coupon',
                          style: TextStyle(
                            fontSize: 13,
                            fontWeight: FontWeight.w500,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildCheckoutBar(BuildContext context) {
    return BlocBuilder<AppBloc, AppState>(
      builder: (context, appState) {
        return BlocBuilder<CartBloc, CartState>(
          builder: (context, cartState) {
            final isAuthenticated = appState.maybeMap(
              loaded: (loaded) => loaded.isAuthenticated,
              orElse: () => false,
            );

            final buttonText = _isProcessingOrder
                ? 'Processing...'
                : (isAuthenticated ? 'Place Order' : 'Login to Place Order');

            return Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(13), // 0.05 * 255 = ~13
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: SafeArea(
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Text(
                            '₹${(cartState.cart.total ?? 0).toStringAsFixed(2)}',
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          const SizedBox(height: 2),
                          Text(
                            'Total (${cartState.cart.totalItems} items)',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.grey[600],
                            ),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(
                      height: 48,
                      child: AppButton(
                        text: buttonText,
                        onPressed: _isProcessingOrder
                            ? null
                            : () => _placeOrder(cartState),
                        backgroundColor: AppColors.primary,
                        textColor: Colors.white,
                        borderRadius: 8,
                        width: 180,
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    );
  }

  // Show address edit modal
  void _showAddressEditModal() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => Padding(
        padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
        ),
        child: Container(
          padding: const EdgeInsets.all(16),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.85,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Modal header
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Edit Delivery Address',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => context.pop(),
                  ),
                ],
              ),
              const Divider(),

              // Save button
              Padding(
                padding: const EdgeInsets.only(top: 16),
                child: AppButton(
                  text: 'Save Address',
                  onPressed: () {
                    if (_addressFormKey.currentState!.validate()) {
                      _addressFormKey.currentState!.save();
                      context.pop();
                    }
                  },
                  backgroundColor: AppColors.primary,
                  textColor: Colors.white,
                  borderRadius: 8,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildAddressCard() {
    final address = _checkoutData['address'] as Map<String, dynamic>;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.location_on,
                color: AppColors.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                address['fullName'],
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: _showAddressEditModal,
                style: TextButton.styleFrom(
                  foregroundColor: AppColors.primary,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                ),
                child: const Text('Edit'),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${address['addressLine1']}, ${address['addressLine2']}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${address['city']}, ${address['state']} - ${address['pincode']}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[800],
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Phone: ${address['phoneNumber']}',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _placeOrder(CartState state) async {
    if (state.cart.items?.isEmpty ?? false) return;

    // Check if user is authenticated before proceeding with checkout
    final appBloc = getIt<AppBloc>();
    if (!appBloc.isAuthenticated) {
      // Navigate to login screen with return route information
      // Store the return route in extra data to be used after login
      context.push(RouteNames.login, extra: {'returnRoute': RouteNames.cart});
      return;
    }

    setState(() {
      _isProcessingOrder = true;
    });

    try {
      // Get the order repository from dependency injection
      final orderRepository = getIt<OrderRepositoryInterface>();

      // Get user data from preferences
      final userDataJson = AppPreferences.getUserdata();
      Map<String, dynamic> userData = {};
      String customerId = 'CUST-001'; // Default fallback
      String customerName = 'nithin'; // Default name as per requirement

      if (userDataJson != null && userDataJson.isNotEmpty) {
        userData = jsonDecode(userDataJson);
        // Use Firebase UID as customer ID if available
        customerId = userData['uid'] ?? customerId;
      }

      // Get facility information from the first item in cart
      final firstItem = state.cart.items?.first;
      final facilityId = firstItem?.facilityId ?? 'FAC-001';
      final facilityName = firstItem?.facilityName ?? 'abc';

      // Format items as per API requirements
      final formattedItems = state.cart.items
              ?.map((item) => {
                    'sku': item.skuID ?? 'ITEM-${item.id}',
                    'quantity': item.quantity ?? 0,
                    'unit_price': item.price ?? 0,
                    // Make sure sale_price is never 0 - use the same as unit_price if no discount
                    'sale_price': (item.discountedPrice != null &&
                            item.discountedPrice! > 0)
                        ? item.discountedPrice!
                        : item.price ?? 0
                  })
              .toList() ??
          [];

      // Use the cart model's total for consistency across screens
      num totalAmount = state.cart.total ?? 0;

      // Ensure total amount is never zero for non-empty carts
      if (totalAmount <= 0 && state.cart.items?.isNotEmpty == true) {
        // Calculate a fallback total if the cart model's total is somehow zero
        state.cart.items?.forEach((item) {
          num itemPrice = (item.price ?? 0);
          num itemTotal = itemPrice * (item.quantity ?? 1);
          totalAmount += itemTotal;
        });

        // Add delivery fee if applicable
        if (totalAmount > 0) {
          if (totalAmount < 200) {
            totalAmount += 60.0; // Base fee + small order fee
          } else if (totalAmount < 500) {
            totalAmount += 40.0; // Base delivery fee
          }
          // Add 5% tax
          totalAmount += (totalAmount * 0.05);
        }
      }

      // Create order using the repository
      final response = await orderRepository.createOrder(
        customerId: customerId,
        customerName: customerName,
        facilityId: facilityId,
        facilityName: facilityName,
        totalAmount: totalAmount,
        items: formattedItems,
      );

      if (response?.statusCode == 200 || response?.statusCode == 201) {
        // Generate order ID from response or generate locally
        final orderId = response?.data['order_id'] ??
            'ORD-${DateTime.now().millisecondsSinceEpoch}';

        // Create order data for success screen
        final orderData = {
          'items': state.cart.items,
          'address': _checkoutData['address'],
          'paymentMethod': _checkoutData['paymentMethod'],
          'total': totalAmount, // Use our correctly calculated total
          'subtotal': state.cart.subTotal,
          'tax': state.cart.tax, // Add tax value
          'discount': state.cart.discount,
          'deliveryFee': state.cart.deliveryFee,
          'itemCount': state.cart.totalItems,
          'orderDate': DateTime.now().toIso8601String(),
        };

        // Clear cart after successful order
        _cartBloc.add(CartEvent.clear());

        // Navigate to order success screen
        if (mounted) {
          context.pushReplacement(
            RouteNames.orderSuccess,
            extra: {
              'orderId': orderId,
              'orderData': orderData,
            },
          );
        }
      } else {
        throw Exception('Failed to create order: ${response?.statusMessage}');
      }
    } catch (e) {
      // Show error message
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to place order: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isProcessingOrder = false;
        });
      }
    }
  }
}
