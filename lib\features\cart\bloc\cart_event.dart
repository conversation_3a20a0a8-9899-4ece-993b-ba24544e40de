import 'package:freezed_annotation/freezed_annotation.dart';
import '../../../data/models/cart_item_model.dart';
part 'cart_event.freezed.dart';

@freezed
class CartEvent with _$CartEvent {
  const factory CartEvent.init() = CartInit;
  const factory CartEvent.addItem({required CartItemModel item}) = CartAddItem;

  const factory CartEvent.removeItem(String itemId) = CartRemoveItem;
  const factory CartEvent.updateQuantity(String itemId, int quantity) =
      CartUpdateQuantity;
  const factory CartEvent.clear() = CartClear;
  const factory CartEvent.applyCoupon(String code) = CartApplyCoupon;
  const factory CartEvent.removeCoupon() = CartRemoveCoupon;
  const factory CartEvent.importData(String jsonData) = CartImportData;
}
