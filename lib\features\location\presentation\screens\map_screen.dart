import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:rozana/routes/app_router.dart';

import '../../../../core/themes/color_schemes.dart';
import '../../../../widgets/custom_button.dart';
import '../../services/adress_services.dart';

class MapScreen extends StatefulWidget {
  final double? initialLatitude;
  final double? initialLongitude;
  final Function(double latitude, double longitude)? onLocationSelected;

  const MapScreen({
    super.key,
    this.initialLatitude,
    this.initialLongitude,
    this.onLocationSelected,
  });

  @override
  State<MapScreen> createState() => _MapScreenState();
}

class _MapScreenState extends State<MapScreen> {
  final AddressService _addressService = AddressService();

  GoogleMapController? _mapController;
  late double _currentLatitude;
  late double _currentLongitude;
  bool _isLoading = false;
  String _currentAddress = 'Loading address...';

  // Add search controller
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void initState() {
    super.initState();
    _initializeLocation();
  }

  void _initializeLocation() {
    // Use provided coordinates or default to a central location
    _currentLatitude = widget.initialLatitude ?? 28.6139; // Delhi default
    _currentLongitude = widget.initialLongitude ?? 77.2090;

    // Get address for initial location
    _getAddressFromCoordinates(_currentLatitude, _currentLongitude);
  }

  Future<void> _getAddressFromCoordinates(
      double latitude, double longitude) async {
    try {
      final placemarks = await _addressService.getAddressFromCoordinates(
        latitude,
        longitude,
      );

      if (placemarks != null && placemarks.isNotEmpty) {
        final placemark = placemarks.first;
        setState(() {
          _currentAddress = _buildAddressString(placemark);
        });
      } else {
        setState(() {
          _currentAddress = 'Address not found';
        });
      }
    } catch (e) {
      setState(() {
        _currentAddress = 'Failed to get address';
      });
    }
  }

  String _buildAddressString(dynamic placemark) {
    List<String> addressParts = [];

    if (placemark.street != null && placemark.street!.isNotEmpty) {
      addressParts.add(placemark.street!);
    }
    if (placemark.locality != null && placemark.locality!.isNotEmpty) {
      addressParts.add(placemark.locality!);
    }
    if (placemark.administrativeArea != null &&
        placemark.administrativeArea!.isNotEmpty) {
      addressParts.add(placemark.administrativeArea!);
    }
    if (placemark.postalCode != null && placemark.postalCode!.isNotEmpty) {
      addressParts.add(placemark.postalCode!);
    }

    return addressParts.isNotEmpty
        ? addressParts.join(', ')
        : 'Unknown location';
  }

  void _onCameraMove(CameraPosition position) {
    // Update coordinates as map moves
    _currentLatitude = position.target.latitude;
    _currentLongitude = position.target.longitude;
  }

  void _onCameraIdle() {
    // Get address when map movement stops
    _getAddressFromCoordinates(_currentLatitude, _currentLongitude);
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final position = await _addressService.getCurrentPosition();

      if (position != null) {
        setState(() {
          _currentLatitude = position.latitude;
          _currentLongitude = position.longitude;
        });

        // Move map to current location
        _mapController?.animateCamera(
          CameraUpdate.newCameraPosition(
            CameraPosition(
              target: LatLng(_currentLatitude, _currentLongitude),
              zoom: 16,
            ),
          ),
        );

        // Get address for current location
        _getAddressFromCoordinates(_currentLatitude, _currentLongitude);
      } else {
        _showSnackBar('Failed to get current location');
      }
    } catch (e) {
      _showSnackBar('Failed to get current location');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _selectLocation() {
    if (widget.onLocationSelected != null) {
      widget.onLocationSelected!(_currentLatitude, _currentLongitude);
    }
    context.pushReplacement(
      RouteNames.editAddress,
      extra: {
      'latitude': _currentLatitude,
      'longitude': _currentLongitude,
      'address': _currentAddress,
    });
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
      ),
    );
  }

  Future<void> _searchAndMoveToLocation(String query) async {
    if (query.trim().isEmpty) return;
    setState(() { _isSearching = true; });
    try {
      final locations = await _addressService.searchAddresses(query);
      if (locations != null && locations.isNotEmpty) {
        final location = locations.first;
        final placemarks = await _addressService.getPlacemarkFromLocation(location);
        if (placemarks != null && placemarks.isNotEmpty) {
          final placemark = placemarks.first;
          setState(() {
            _currentLatitude = location.latitude;
            _currentLongitude = location.longitude;
            _currentAddress = _buildAddressString(placemark);
          });
          _mapController?.animateCamera(
            CameraUpdate.newCameraPosition(
              CameraPosition(
                target: LatLng(_currentLatitude, _currentLongitude),
                zoom: 16,
              ),
            ),
          );
        } else {
          _showSnackBar('Address not found for this location');
        }
      } else {
        _showSnackBar('Location not found');
      }
    } catch (e) {
      _showSnackBar('Failed to search location');
    } finally {
      setState(() { _isSearching = false; });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Select Location'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
        ),
        actions: [
          IconButton(
            onPressed: _isLoading ? null : _getCurrentLocation,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(Icons.my_location),
            tooltip: 'Get Current Location',
          ),
        ],
      ),
      body: Stack(
        children: [
          // Google Map
          GoogleMap(
            initialCameraPosition: CameraPosition(
              target: LatLng(_currentLatitude, _currentLongitude),
              zoom: 16,
            ),
            onMapCreated: (controller) {
              _mapController = controller;
            },
            onCameraMove: _onCameraMove,
            onCameraIdle: _onCameraIdle,
            myLocationEnabled: true,
            myLocationButtonEnabled: false, // We have our own button
            zoomControlsEnabled: true,
            mapToolbarEnabled: false,
          ),

          // Search bar at the top
          Positioned(
            top: 20,
            left: 16,
            right: 16,
            child: Material(
              elevation: 4,
              borderRadius: BorderRadius.circular(8),
              child: TextField(
                controller: _searchController,
                textInputAction: TextInputAction.search,
                onSubmitted: _searchAndMoveToLocation,
                decoration: InputDecoration(
                  hintText: 'Search for a place or address',
                  prefixIcon: const Icon(Icons.search),
                  suffixIcon: _isSearching
                      ? const Padding(
                          padding: EdgeInsets.all(10),
                          child: SizedBox(
                            width: 16,
                            height: 16,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                        )
                      : (_searchController.text.isNotEmpty
                          ? IconButton(
                              icon: const Icon(Icons.clear),
                              onPressed: () {
                                _searchController.clear();
                              },
                            )
                          : null),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.symmetric(vertical: 14),
                ),
              ),
            ),
          ),

          // Static marker in center
          const Center(
            child: Icon(
              Icons.location_pin,
              size: 40,
              color: AppColors.primary,
            ),
          ),

          // Address info card
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 34),
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.vertical(top: Radius.circular(16)),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 10,
                    offset: Offset(0, -2),
                  ),
                ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text(
                    'Selected Location',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _currentAddress,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                  const SizedBox(height: 16),
                  CustomButton(
                    text: 'Select This Location',
                    onPressed: _selectLocation,
                    backgroundColor: AppColors.primary,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
