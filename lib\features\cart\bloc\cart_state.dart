import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:rozana/data/models/cart_model.dart';

part 'cart_state.freezed.dart';

@freezed
class CartState with _$CartState {
  const factory CartState({
    required CartModel cart,
    required bool isLoading,
    String? appliedCoupon,
    String? error,
  }) = _CartState;

  factory CartState.initial() => CartState(
        cart: CartModel(),
        isLoading: false,
        appliedCoupon: null,
        error: null,
      );
}
