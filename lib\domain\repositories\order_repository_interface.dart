import 'package:dio/dio.dart';

abstract class OrderRepositoryInterface {
  /// Creates a new order with the provided data
  /// 
  /// Returns the API response if successful, or throws an exception if failed
  Future<Response?> createOrder({
    required String customerId,
    required String customerName,
    required String facilityId,
    required String facilityName,
    required num totalAmount,
    required List<Map<String, dynamic>> items,
  });
}
