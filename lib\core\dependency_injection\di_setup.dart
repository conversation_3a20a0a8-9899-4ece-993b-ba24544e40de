import 'package:rozana/core/services/token_refresh_service.dart';
import 'package:rozana/core/themes/theme_bloc/theme_bloc.dart';
import 'package:rozana/data/repositories/auth_repository_impl.dart';
import 'package:rozana/data/repositories/home_repository_impl.dart';
import 'package:rozana/data/repositories/order_repository_impl.dart';
import 'package:rozana/domain/repositories/order_repository_interface.dart';
import 'package:rozana/data/services/data_loading_manager.dart';
import 'package:rozana/data/services/sample_data.dart';
import 'package:rozana/domain/repositories/home_repository_interface.dart';
import 'package:rozana/domain/usecases/get_banners_usecase.dart';
import 'package:rozana/domain/usecases/get_categories_usecase.dart';
import 'package:rozana/domain/usecases/get_products_usecase.dart';
import 'package:rozana/features/cart/bloc/cart_bloc.dart';
import 'package:rozana/features/categories/bloc/categories_bloc.dart';
import 'package:rozana/features/location/bloc/location%20bloc/location_bloc.dart';
import 'package:rozana/features/location/services/adress_services.dart';
import 'package:rozana/features/location/services/locaion_services.dart';
import 'package:rozana/features/profile/bloc/profile_bloc.dart';
import 'package:rozana/features/search/bloc/bloc/search_bloc.dart';
import 'package:rozana/features/search/services/typesense_service.dart';
import 'package:rozana/web-view/bloc/bloc/web_view_bloc.dart';

import '../../app/bloc/app_bloc.dart';
import '../../domain/repositories/auth_repository_interface.dart';
import '../../features/auth/bloc/login_bloc/login_bloc.dart';
import '../../features/home/<USER>/home bloc/home_bloc.dart';

// Import the global GetIt instance
import '../../features/products/bloc/product_listing_bloc.dart';
import '../utils/notifier.dart';
import 'di_container.dart';

void setupDI() {
  // Register Notifiers
  getIt.registerLazySingleton<AppNotifier>(
    () => NotificationServiceImpl(scaffoldMessengerKey),
  );

  // Register repositories (singletons as they often manage external resources)
  getIt.registerLazySingleton<IAuthRepository>(() => AuthRepositoryImpl());
  getIt.registerLazySingleton<OrderRepositoryInterface>(
      () => OrderRepositoryImpl());

  // Register TokenRefreshService for handling Firebase auth token refreshes
  getIt.registerLazySingleton<TokenRefreshService>(() => TokenRefreshService());
  // Register services (singletons as they often manage external resources)
  getIt.registerLazySingleton<LocationService>(
      () => LocationService()); //loaction service as singleton
  getIt.registerLazySingleton<AddressService>(
      () => AddressService()); //address service as singleton
  getIt.registerLazySingleton<TypesenseService>(
      () => TypesenseService()); //typesense service as singleton
  getIt.registerLazySingleton<DataLoadingManager>(() => DataLoadingManager(
        dataService: SampleDataService(),
        typesenseService: getIt<TypesenseService>(),
      )); //Data loading manager with injected dependencies
  getIt.registerLazySingleton<IHomeRepository>(() => HomeRepositoryImpl(
      dataManager: getIt<
          DataLoadingManager>())); //Home repository with injected dependency

  // Register use cases (factories as they are stateless and can be created per use)
  getIt.registerFactory<GetCategoriesUseCase>(
      () => GetCategoriesUseCase(getIt<IHomeRepository>()));
  getIt.registerFactory<GetProductsUseCase>(
      () => GetProductsUseCase(getIt<IHomeRepository>()));
  getIt.registerFactory<GetBannersUseCase>(
      () => GetBannersUseCase(getIt<IHomeRepository>()));

  // Register BLoCs (typically as factories as they often have state and are created per-screen/per-widget)
  getIt.registerLazySingleton<AppBloc>(() => AppBloc());
  getIt.registerLazySingleton<ThemeBloc>(() => ThemeBloc());
  getIt.registerLazySingleton<LocationBloc>(() => LocationBloc(
      locationService: getIt<LocationService>(),
      addressService: getIt<AddressService>()));
  getIt.registerLazySingleton<CartBloc>(() => CartBloc());
  getIt.registerLazySingleton<WebViewBloc>(() => WebViewBloc());
  getIt.registerFactory<LoginBloc>(() => LoginBloc(
        authRepository: getIt<IAuthRepository>(),
        notifier: getIt<AppNotifier>(),
        appBloc: getIt<AppBloc>(),
      ));
  getIt.registerFactory<HomeBloc>(() => HomeBloc(
        getCategoriesUseCase: getIt<GetCategoriesUseCase>(),
        getProductsUseCase: getIt<GetProductsUseCase>(),
        getBannersUseCase: getIt<GetBannersUseCase>(),
      ));
  getIt
      .registerFactory<SearchBloc>(() => SearchBloc(getIt<TypesenseService>()));

  getIt.registerFactory<CategoriesBloc>(
      () => CategoriesBloc(getIt<GetCategoriesUseCase>()));
  getIt.registerFactory<ProductListingBloc>(() => ProductListingBloc());
  getIt.registerFactory<ProfileBloc>(() => ProfileBloc());
}
